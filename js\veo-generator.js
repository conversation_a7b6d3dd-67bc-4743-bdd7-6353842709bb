// Veo 2 Generator Functionality
//
// NOTE: This is currently a mock implementation for testing the UI.
// To implement actual Veo video generation, you need to:
// 1. Set up Google Cloud Project with Vertex AI API enabled
// 2. Use the proper Vertex AI endpoint: https://us-central1-aiplatform.googleapis.com/v1/projects/{PROJECT_ID}/locations/us-central1/publishers/google/models/veo-2.0-generate-001:predict
// 3. Implement proper authentication (OAuth2 or Service Account)
// 4. Use the correct request format as shown in the Google Cloud documentation
//
// For now, this implementation shows a working UI with mock video generation.

document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const veoPrompt = document.getElementById('veoPrompt');
    const veoNegativePrompt = document.getElementById('veoNegativePrompt');
    const veoModel = document.getElementById('veoModel');
    const veoCount = document.getElementById('veoCount');
    const veoDuration = document.getElementById('veoDuration');
    const veoAspectRatio = document.getElementById('veoAspectRatio');
    const veoPersonGeneration = document.getElementById('veoPersonGeneration');
    const generateVideoBtn = document.getElementById('generateVideoBtn');
    const veoResults = document.getElementById('veoResults');
    const veoGallery = document.getElementById('veoGallery');
    const veoLoading = document.getElementById('veoLoading');
    const veoProgressBar = document.getElementById('veoProgressBar');
    const veoProgressText = document.getElementById('veoProgressText');
    const downloadAllVideosBtn = document.getElementById('downloadAllVideosBtn');
    const clearVideosBtn = document.getElementById('clearVideosBtn');

    // Image upload elements
    const veoSourceImage = document.getElementById('veoSourceImage');
    const veoImagePreview = document.getElementById('veoImagePreview');
    const veoImagePlaceholder = document.getElementById('veoImagePlaceholder');
    const removeVeoImageBtn = document.getElementById('removeVeoImageBtn');
    const selectVeoImageBtn = document.getElementById('selectVeoImageBtn');
    const veoImageInput = document.getElementById('veoImageInput');

    // Get the placeholder element
    const veoPlaceholder = document.getElementById('veoPlaceholder');

    // Check if elements exist
    if (!generateVideoBtn) {
        console.warn('Veo generator elements not found');
        return;
    }

    // Debug: Log all important elements
    console.log('Veo elements check:');
    console.log('generateVideoBtn:', generateVideoBtn);
    console.log('veoResults:', veoResults);
    console.log('veoGallery:', veoGallery);
    console.log('veoLoading:', veoLoading);
    console.log('veoPlaceholder:', veoPlaceholder);

    // Ensure all required elements exist
    if (!veoResults || !veoGallery || !veoLoading) {
        console.error('Critical Veo elements missing!');
        console.error('veoResults:', veoResults);
        console.error('veoGallery:', veoGallery);
        console.error('veoLoading:', veoLoading);
        return;
    }

    // Handle generate button click
    if (!generateVideoBtn.hasAttribute('data-listener-added')) {
        generateVideoBtn.addEventListener('click', function() {
            // Validate prompt
            const hasPrompt = veoPrompt.value.trim() !== '';
            if (!hasPrompt) {
                showToast('Please enter a prompt for video generation', 'warning');
                return;
            }

            // Check if API key is available
            const apiKey = getActiveApiKey();
            if (!apiKey) {
                showToast('No API key found. Please add a Gemini API key in Settings', 'error');
                return;
            }

            // Show loading indicator
            veoLoading.style.display = 'block';
            veoResults.style.display = 'none';
            if (veoPlaceholder) {
                veoPlaceholder.style.display = 'none';
            }

            // Reset progress
            updateProgress(0, 'Starting video generation...');

            // Prepare request parameters
            const params = {
                model: veoModel.value,
                prompt: veoPrompt.value.trim(),
                numberOfVideos: parseInt(veoCount.value),
                durationSeconds: parseInt(veoDuration.value),
                aspectRatio: veoAspectRatio.value,
                personGeneration: veoPersonGeneration.value
            };

            // Add source image if provided
            const hasImage = veoSourceImage && veoSourceImage.src && !veoSourceImage.src.endsWith('#');
            if (hasImage) {
                params.sourceImage = veoSourceImage.src;
            }

            // Add negative prompt if provided
            if (veoNegativePrompt.value.trim()) {
                params.negativePrompt = veoNegativePrompt.value.trim();
            }

            console.log("Prepared Veo parameters:", params);

            // Call the Veo API
            generateVideosWithVeo(apiKey, params)
                .then(displayGeneratedVideos)
                .catch(error => {
                    console.error('Error generating videos:', error);
                    showToast('Error generating videos: ' + error.message, 'error');
                    veoLoading.style.display = 'none';
                    if (veoPlaceholder) {
                        veoPlaceholder.style.display = 'block';
                    }
                });
        });
        generateVideoBtn.setAttribute('data-listener-added', 'true');
    }

    // Handle download all videos button
    if (!downloadAllVideosBtn.hasAttribute('data-listener-added')) {
        downloadAllVideosBtn.addEventListener('click', function() {
            const videos = veoGallery.querySelectorAll('video');
            if (videos.length === 0) {
                showToast('No videos to download', 'warning');
                return;
            }

            // Download all videos
            downloadAllVideos(videos);
        });
        downloadAllVideosBtn.setAttribute('data-listener-added', 'true');
    }

    // Handle clear videos button
    if (!clearVideosBtn.hasAttribute('data-listener-added')) {
        clearVideosBtn.addEventListener('click', function() {
            veoGallery.innerHTML = '';
            veoResults.style.display = 'none';
            if (veoPlaceholder) {
                veoPlaceholder.style.display = 'block';
            }
        });
        clearVideosBtn.setAttribute('data-listener-added', 'true');
    }

    // Handle image upload
    if (selectVeoImageBtn && veoImageInput) {
        if (!selectVeoImageBtn.hasAttribute('data-listener-added')) {
            selectVeoImageBtn.addEventListener('click', function() {
                veoImageInput.click();
            });
            selectVeoImageBtn.setAttribute('data-listener-added', 'true');
        }

        if (!veoImageInput.hasAttribute('data-listener-added')) {
            veoImageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const file = this.files[0];
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        veoSourceImage.src = e.target.result;
                        veoImagePreview.classList.remove('d-none');
                        veoImagePlaceholder.classList.add('d-none');
                    };

                    reader.readAsDataURL(file);
                }
            });
            veoImageInput.setAttribute('data-listener-added', 'true');
        }
    }

    // Handle image removal
    if (removeVeoImageBtn) {
        if (!removeVeoImageBtn.hasAttribute('data-listener-added')) {
            removeVeoImageBtn.addEventListener('click', function() {
                veoSourceImage.src = '';
                veoImagePreview.classList.add('d-none');
                veoImagePlaceholder.classList.remove('d-none');
                veoImageInput.value = '';
            });
            removeVeoImageBtn.setAttribute('data-listener-added', 'true');
        }
    }

    // Function to get active API key
    function getActiveApiKey() {
        try {
            if (typeof window.getApiKey === 'function') {
                const apiKey = window.getApiKey();
                if (apiKey) {
                    return apiKey;
                }
            }
        } catch (e) {
            console.error("Error accessing window.getApiKey:", e);
        }

        // Fallback to localStorage
        const apiKey = localStorage.getItem('csvision_api_key');
        if (apiKey) {
            return apiKey;
        }

        // Try to get from apiKeys array
        const apiKeys = JSON.parse(localStorage.getItem('csvision_api_keys') || '[]');
        if (apiKeys.length > 0) {
            const activeKey = apiKeys.find(key => key.active);
            if (activeKey) {
                return activeKey.key;
            } else if (apiKeys[0]) {
                return apiKeys[0].key;
            }
        }

        return null;
    }

    // Function to update progress
    function updateProgress(percentage, text) {
        if (veoProgressBar) {
            veoProgressBar.style.width = percentage + '%';
        }
        if (veoProgressText) {
            veoProgressText.textContent = text;
        }
    }

    // Function to generate videos using Veo API
    async function generateVideosWithVeo(apiKey, params) {
        try {
            console.log("Generating videos with Veo params:", params);
            updateProgress(10, 'Preparing request...');

            const model = params.model || "veo-2.0-generate-001";

            // Build the prompt
            let fullPrompt = params.prompt || '';

            // Add negative prompt if provided
            if (params.negativePrompt) {
                fullPrompt += ` Please avoid: ${params.negativePrompt}`;
            }

            // Prepare the API URL - using the Google GenAI SDK approach
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateVideos?key=${apiKey}`;

            updateProgress(20, 'Sending request to Veo API...');

            // Build request body according to the Veo API format from the documentation
            const requestBody = {
                prompt: fullPrompt,
                config: {
                    aspectRatio: params.aspectRatio,
                    personGeneration: params.personGeneration,
                    numberOfVideos: params.numberOfVideos,
                    durationSeconds: params.durationSeconds
                }
            };

            // Add source image if provided
            if (params.sourceImage) {
                // Extract base64 data
                const imageData = params.sourceImage.split(',')[1];
                const mimeType = params.sourceImage.split(';')[0].split(':')[1];

                requestBody.image = {
                    imageBytes: imageData,
                    mimeType: mimeType
                };
            }

            console.log("Sending request to Veo API:", requestBody);

            // Start the video generation operation
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.text();
                throw new Error(`Veo API error: ${response.status} - ${errorData}`);
            }

            const operationData = await response.json();
            console.log("Operation started:", operationData);

            if (!operationData.name) {
                throw new Error('No operation name returned from Veo API');
            }

            updateProgress(30, 'Video generation started...');

            // Poll for completion using the operations endpoint
            const videos = await pollForCompletion(apiKey, operationData.name);
            return videos;





        } catch (error) {
            console.error('Error in generateVideosWithVeo:', error);
            throw error;
        }
    }

    // Function to poll for video generation completion
    async function pollForCompletion(apiKey, operationName) {
        const maxAttempts = 60; // Maximum 10 minutes (60 * 10 seconds)
        let attempts = 0;

        while (attempts < maxAttempts) {
            try {
                updateProgress(30 + (attempts * 60 / maxAttempts), `Generating videos... ${Math.floor(attempts * 10 / 60)}:${String(attempts * 10 % 60).padStart(2, '0')}`);

                // Check operation status
                const statusUrl = `https://generativelanguage.googleapis.com/v1beta/${operationName}?key=${apiKey}`;

                const statusResponse = await fetch(statusUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!statusResponse.ok) {
                    const errorData = await statusResponse.text();
                    throw new Error(`Status check error: ${statusResponse.status} - ${errorData}`);
                }

                const statusData = await statusResponse.json();
                console.log(`Operation status (attempt ${attempts + 1}):`, statusData);

                // Check if operation is done
                if (statusData.done) {
                    updateProgress(90, 'Processing generated videos...');

                    if (statusData.error) {
                        throw new Error(`Video generation failed: ${statusData.error.message || 'Unknown error'}`);
                    }

                    if (statusData.response && statusData.response.generatedVideos) {
                        updateProgress(100, 'Videos generated successfully!');
                        return statusData.response.generatedVideos;
                    } else {
                        throw new Error('No videos found in completed operation');
                    }
                }

                // Wait 10 seconds before next check
                await new Promise(resolve => setTimeout(resolve, 10000));
                attempts++;

            } catch (error) {
                console.error('Error polling for completion:', error);
                throw error;
            }
        }

        throw new Error('Video generation timed out after 10 minutes');
    }

    // Function to get active API key - using the same function as in imagen generation
    function getActiveApiKey() {
        // Try to get it from the global window object
        try {
            // Check if we're in the context of the main application
            if (typeof window.getApiKey === 'function') {
                const apiKey = window.getApiKey();
                if (apiKey) {
                    return apiKey;
                }
            }
        } catch (e) {
            console.error("Error accessing window.getApiKey:", e);
        }

        // Check if API key rotation is enabled
        const enableApiKeyRotation = localStorage.getItem('csvision_enable_api_key_rotation') === 'true';

        // If rotation is enabled and we have a getNextApiKey function, try to use it
        if (enableApiKeyRotation && typeof window.getNextApiKey === 'function') {
            try {
                const nextKey = window.getNextApiKey();
                if (nextKey) {
                    console.log("Using next API key from rotation for video generation");
                    return nextKey;
                }
            } catch (e) {
                console.error("Error accessing window.getNextApiKey:", e);
            }
        }

        // Fallback to localStorage directly
        const apiKey = localStorage.getItem('csvision_api_key');
        if (apiKey) {
            return apiKey;
        }

        // Try to get from apiKeys array if it exists
        const apiKeys = JSON.parse(localStorage.getItem('csvision_api_keys') || '[]');
        if (apiKeys.length > 0) {
            const activeKey = apiKeys.find(key => key.active);
            if (activeKey) {
                return activeKey.key;
            } else if (apiKeys[0]) {
                return apiKeys[0].key;
            }
        }

        // Last resort - try the legacy apiKeys array if it exists
        const legacyApiKeys = JSON.parse(localStorage.getItem('apiKeys') || '[]');
        const activeKeyIndex = parseInt(localStorage.getItem('activeApiKeyIndex') || '0');

        if (legacyApiKeys.length > 0 && activeKeyIndex >= 0 && activeKeyIndex < legacyApiKeys.length) {
            return legacyApiKeys[activeKeyIndex].key;
        }

        return null;
    }





    // Function to display generated videos
    function displayGeneratedVideos(generatedVideos) {
        try {
            console.log("Displaying videos:", generatedVideos);
            console.log("veoLoading element:", veoLoading);
            console.log("veoGallery element:", veoGallery);
            console.log("veoResults element:", veoResults);

            veoLoading.style.display = 'none';
            veoGallery.innerHTML = '';

            if (!generatedVideos || generatedVideos.length === 0) {
                throw new Error('No videos generated');
            }

            generatedVideos.forEach((generatedVideo, index) => {
                console.log(`Processing video ${index}:`, generatedVideo);
                if (generatedVideo.video && generatedVideo.video.uri) {
                    console.log(`Creating video element for URI: ${generatedVideo.video.uri}`);
                    createVideoElement(generatedVideo.video.uri, index);
                } else {
                    console.warn(`Video ${index} has no valid URI:`, generatedVideo);
                }
            });

            console.log("Setting veoResults display to block");
            veoResults.style.display = 'block';

            if (veoPlaceholder) {
                console.log("Hiding placeholder");
                veoPlaceholder.style.display = 'none';
            }

            showToast(`Successfully generated ${generatedVideos.length} video(s)!`, 'success');

        } catch (error) {
            console.error('Error displaying videos:', error);
            showToast('Error displaying videos: ' + error.message, 'error');
            veoLoading.style.display = 'none';
            if (veoPlaceholder) {
                veoPlaceholder.style.display = 'block';
            }
        }
    }

    // Function to create video element
    function createVideoElement(videoUri, index) {
        console.log(`Creating video element ${index} with URI: ${videoUri}`);

        const col = document.createElement('div');
        col.className = 'col-md-6 mb-2';

        const card = document.createElement('div');
        card.className = 'card veo-card';

        // Create actual video element for real videos
        const video = document.createElement('video');
        video.className = 'card-img-top';
        video.style.width = '100%';
        video.style.height = 'auto';
        video.controls = true;
        video.preload = 'metadata';

        // Set video source with API key for authentication
        video.src = `${videoUri}&key=${getActiveApiKey()}`;
        console.log(`Set video source: ${videoUri}`);

        // Add error handling for video loading
        video.addEventListener('loadstart', () => {
            console.log(`Video ${index} started loading`);
        });

        video.addEventListener('loadeddata', () => {
            console.log(`Video ${index} data loaded`);
        });

        video.addEventListener('error', (e) => {
            console.error(`Video ${index} error:`, e);
            console.error(`Video ${index} error details:`, video.error);

            // Fallback: create a simple colored div with error message
            video.style.display = 'none';
            const fallback = document.createElement('div');
            fallback.style.width = '100%';
            fallback.style.height = '200px';
            fallback.style.backgroundColor = '#dc3545';
            fallback.style.display = 'flex';
            fallback.style.alignItems = 'center';
            fallback.style.justifyContent = 'center';
            fallback.style.color = 'white';
            fallback.style.fontSize = '16px';
            fallback.style.fontWeight = 'bold';
            fallback.style.textAlign = 'center';
            fallback.innerHTML = `<div>Video ${index + 1}<br><small>Failed to load</small></div>`;
            card.insertBefore(fallback, video);
        });

        const cardBody = document.createElement('div');
        cardBody.className = 'card-body p-2';

        const downloadBtn = document.createElement('button');
        downloadBtn.className = 'btn btn-sm btn-primary-purple text-white bi bi-download me-1';
        downloadBtn.textContent = ' Download';
        downloadBtn.onclick = () => downloadVideo(video.src, `veo_video_${index + 1}.mp4`);

        cardBody.appendChild(downloadBtn);
        card.appendChild(video);
        card.appendChild(cardBody);
        col.appendChild(card);

        console.log(`Appending video ${index} to gallery`);
        veoGallery.appendChild(col);

        console.log(`Video element ${index} created and added to DOM`);
    }

    // Function to download video
    function downloadVideo(videoUrl, filename) {
        const a = document.createElement('a');
        a.href = videoUrl;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    // Function to download all videos
    function downloadAllVideos(videos) {
        videos.forEach((video, index) => {
            setTimeout(() => {
                downloadVideo(video.src, `veo_video_${index + 1}.mp4`);
            }, index * 1000); // Delay each download by 1 second
        });
        showToast(`Downloading ${videos.length} video(s)...`, 'info');
    }
});
